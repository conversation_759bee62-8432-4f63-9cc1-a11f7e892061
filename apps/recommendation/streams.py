import json
from typing import Generator, Union, Dict, Any

class StreamChunk:

    @staticmethod
    def text(content: str) -> str:
        """文本块 0:string\n (Type ID=0)"""
        return f'0:{json.dumps(content, ensure_ascii=False)}\n'

    @staticmethod
    def sourse(content: str) ->bytes:
        # return f'h: {json.dumps({"sourceType": "url", "id": "source-id", "url": "https://example.com", "title": "Example"})}\n'
        # return  '9:{{"toolCallId":"{id}","toolName":"{name}","args":{args}}}\n'
        # return 'h:{"sourceType": "url", "id": "source-id", "url": "http://www.baidu.com", "title": "Example"}\n'
        # return 'h: {"type": "document", "id": "doc123", "url": "https://example.com/doc.pdf"}\n'
        # return 'h: {"type": "url", "id": "doc123", "url": "http://www.baidu.com","title":"护理222"}\n'
        return 'h:{"sourceType": "url", "id": "source-id", "url": "http://www.baidu.com", "title": "Example"}\n'.encode('utf-8')
    # h: {{"sourceType": "url", "id": "source-id", "url": "https://example.com", "title": "Example"}}\n
    @staticmethod
    def reasoning(content: str) -> bytes:
        """推理块 g:string\\n"""
        # return f'g:{json.dumps(content,ensure_ascii=False)}\n'
        return f'g:{json.dumps(content, ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def redacted_reasoning(data: str) -> bytes:
        """脱敏推理块 i:{"data": string}\\n"""
        # return f'i:{json.dumps({"data": data})}\n'.encode('utf-8')

        # return f'i:{json.dumps({"data": data},  ensure_ascii=False)}\n'

        return f'i:{json.dumps({"data": data}, ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def finish_message(finish_reason: str, usage: Dict[str, int]) -> bytes:
        """结束消息块 d:{finishReason, usage}\\n"""
        # return f'd:{json.dumps({"finishReason": finish_reason, "usage": usage})}\n'.encode('utf-8')
        return f'd:{json.dumps({"finishReason": finish_reason, "usage": usage}, ensure_ascii=False)}\n'.encode('utf-8')

    # 其他类型块按同样模式实现...
    @staticmethod
    def error(message: str) -> bytes:
        """错误块 3:string\\n"""
        return f'3:{json.dumps(message,ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def tool_call(tool_call_id: str, tool_name: str, args: Dict[str, Any]) -> bytes:
        """工具调用块 9:{toolCallId, toolName, args}\\n"""
        return f'9:{json.dumps({"toolCallId": tool_call_id, "toolName": tool_name, "args": args},ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def finish_step(
            finish_reason: str,
            usage: Dict[str, int],
            is_continued: bool
    ) -> bytes:
        """结束步骤块 e:{finishReason, usage, isContinued}\\n"""
        return f'e:{json.dumps({
            "finishReason": finish_reason,
            "usage": usage,
            "isContinued": is_continued
        }, ensure_ascii=False)}\n'.encode('utf-8')