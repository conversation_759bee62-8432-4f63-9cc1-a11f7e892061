# from rest_framework.permissions import BasePermission
# class IsStudent(BasePermission):
#     """
#     仅允许学生访问
#     """
#     def has_permission(self, request, view):
#         return request.user.is_authenticated and request.user.role == 'student'
# class IsTeacher(BasePermission):
#     """
#     仅允许老师访问
#     """
#     def has_permission(self, request, view):
#         return request.user.is_authenticated and request.user.role == 'teacher'

from rest_framework.permissions import BasePermission
from guardian.shortcuts import get_perms

class CanViewDocument(BasePermission):
    """
    仅允许具有 view_document 权限的用户访问
    """
    def has_object_permission(self, request, view, obj):
        return 'view_document' in get_perms(request.user, obj)


class CanDownloadDocument(BasePermission):
    """
    仅允许具有 download_document 权限的用户访问
    """
    def has_permission(self, request, view):
        # 阻止未认证用户
        return request.user.is_authenticated
    def has_object_permission(self, request, view, obj):
        return 'download_document' in get_perms(request.user, obj)