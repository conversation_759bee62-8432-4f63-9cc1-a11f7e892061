import sys
from multiprocessing.connection import answer_challenge
from multiprocessing.util import sub_warning
from time import sleep

from django.contrib.contenttypes.models import ContentType
from django.shortcuts import render
from django.utils.encoding import escape_uri_path
from openai import OpenAI
# Create your views here.
from rest_framework.decorators import api_view, action
from rest_framework.fields import Char<PERSON><PERSON>
from rest_framework.response import Response
from rest_framework import status
import requests
import json
from urllib.parse import unquote
from django.test.client import RequestFactory
import os
from datetime import datetime
from rest_framework.decorators import api_view
from rest_framework.response import Response
import os
from urllib.parse import unquote
import re
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.http import JsonResponse, StreamingHttpResponse
from rest_framework.serializers import Serializer
from sseclient import SSEClient
from xlrd.formatting import fill_in_standard_formats
from yaml import StreamStartEvent

from . import streams
from .models import CustomUser, Document
from .permissions import CanViewDocument, CanDownloadDocument
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, parser_classes
from rest_framework.parsers import JSONParser, MultiPartParser, FormParser
from guardian.shortcuts import assign_perm

from .streams import StreamChunk

BASE_URL = "http://*************:20000"
# BEARER_TOKEN = "ragflow-I0ZjRlMjhlMzQ1ZTExZjA4NzVkOGUxZj"
BEARER_TOKEN = "ragflow-g4NGM2MDhlNDYwZTExZjA4NWZmNWU3Zm"

HEADERS = {
    "Content-Type": "application/json;text/event-stream;charset=utf-8",
    "Authorization": f"Bearer {BEARER_TOKEN}"
}


# 返回格式规范
def sucess_api_response(data=None, status_code=200):
    return Response({
        "data": data,
    }, status=status_code)


def fail_api_response(error=None, status_code=500):
    '''
    {} 里的内容是返回给前端的实际数据。
    status= 是告诉浏览器/客户端本次请求的 HTTP 状态码。
    '''
    return Response({
        "error": error,
    }, status=status_code)


@api_view(['POST'])
def assign_document_permission(request):
    """
    为用户分配文档权限
    请求参数：
    - user_id: 用户ID
    - document_id: 文档ID
    - permission: 权限名称 (如 'can_view_document' 或 'can_download_document')
    """
    user_id = request.data.get('user_id')
    document_id = request.data.get('document_id')
    permission = request.data.get('permission')

    try:
        user = CustomUser.objects.get(id=user_id)
        document = Document.objects.get(id=document_id)

        # 分配权限
        assign_perm(permission, user, document)
        return sucess_api_response(
            {"message": f"成功为用户 {user.username} 分配权限 {permission} 到文档 {document.title}"})
    except Exception as e:
        return fail_api_response({"error": str(e)})


# @api_view(['POST'])
# @permission_classes([CanViewDocument])
def create_session(request, chat_id):
    """创建新会话接口"""
    url = f"{BASE_URL}/api/v1/chats/{chat_id}/sessions"
    try:
        response = requests.post(url, headers=HEADERS, json={"name": "API Session"})
        response.raise_for_status()  # 是 requests 库推荐的错误检查方式，可以让你不用手动判断 status_code，直接用异常机制处理请求失败。
        return sucess_api_response(
            data={"session_id": response.json()["data"]["id"]},
            status_code=response.status_code
        )

    except Exception as e:
        return fail_api_response(f"会话创建失败: {str(e)}", status_code=response.status_code)


# 从rag的回复result["data"]中提取参考文档ID和数据库ID
def extract_reference_ids(data):
    result = {
        "request_id": data.get("id"),
        "documents": []
    }

    # 提取参考文档ID和数据库ID（dataset_id）
    seen = set()  # 用于去重
    for chunk in data.get("reference", {}).get("chunks", []):
        doc_info = {
            "document_id": chunk.get("document_id"),
            "dataset_id": chunk.get("dataset_id"),
            "document_name": chunk.get("document_name")
        }
        # 通过元组去重
        key = (doc_info["document_id"], doc_info["dataset_id"])
        if key not in seen:
            seen.add(key)
            result["documents"].append(doc_info)

    return result


@api_view(['POST'])
@permission_classes([CanViewDocument])  # 允许具有 view_document 权限的用户访问
def ask_question(request, chat_id):
    """提问接口"""
    required_fields = ['session_id', 'question']

    if not all(field in request.data for field in required_fields):
        return fail_api_response(
            f"缺少必要参数: {', '.join(required_fields)}",
            status_code=status.HTTP_400_BAD_REQUEST
        )

    session_id = request.data['session_id']
    question = request.data['question']

    # 调用RAG服务
    rag_url = f"{BASE_URL}/api/v1/chats/{chat_id}/completions"
    payload = {
        "question": question,
        "stream": False,
        "session_id": session_id
    }

    try:
        response = requests.post(rag_url, headers=HEADERS, json=payload)
        response.raise_for_status()
        if (response.raise_for_status() == 200):
            print('ragflow响应成功')
        rag_data = json.loads(response.text)

        # 解析答案
        answer_content = rag_data["data"]["answer"]
        parts = answer_content.split("</think>")
        main_answer = parts[1].strip() if len(parts) > 1 else ""

        # 提取参考文档ID和数据库ID
        reference_data = extract_reference_ids(rag_data['data'])
        # print(reference_data)
        return sucess_api_response(
            data={
                "answer": main_answer,
                "reference": reference_data["documents"]
            },
            status_code=status.HTTP_200_OK
        )


    except Exception as e:
        return Response({
            "error": f"服务器错误: {str(e)}"
        }, status=status.HTTP_500_SERVICE_UNAVAILABLE)


def extract_filename(content_disposition: str) -> str:
    """
    从 Content-Disposition 头部提取文件名，并处理为中文。
    """
    # 默认文件名
    default_filename = "default.md"

    # 如果 Content-Disposition 为空，返回默认文件名
    if not content_disposition:
        return default_filename

    # 优先匹配 filename* 字段
    match = re.search(r"filename\*=(?:UTF-8'')?(.+)", content_disposition, re.IGNORECASE)
    if match:
        # 解码 URL 编码的文件名
        filename = unquote(match.group(1))
        return filename

    # 如果 filename* 不存在，尝试匹配 filename 字段
    match = re.search(r'filename=["\']?([^"\';]+)', content_disposition, re.IGNORECASE)
    if match:
        filename = match.group(1)
        return filename

    # 如果都未匹配到，返回默认文件名
    return default_filename


'''
通过database_id和document_id下载文档
'''


@api_view(['GET'])
@permission_classes([CanDownloadDocument, IsAuthenticated])  # 允许具有 download_document 权限的用户访问
def download_document(request, database_id, document_id):
    try:
        # 配置下载目录
        DOWNLOAD_DIR = os.path.join(settings.BASE_DIR, 'downloads')
        os.makedirs(DOWNLOAD_DIR, exist_ok=True)

        # 构建目标URL
        target_url = f"{BASE_URL}/api/v1/datasets/{database_id}/documents/{document_id}"
        response = requests.get(target_url, headers=HEADERS, stream=True)
        response.raise_for_status()

        # print(response.headers)
        # 提取文件名
        raw_filename = extract_filename(response.headers.get("Content-Disposition", ""))
        print('文件名', raw_filename)
        safe_filename = os.path.basename(raw_filename)
        file_path = os.path.join(DOWNLOAD_DIR, safe_filename)

        # 流式写入文件
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        # 使用 Django 的 HttpRequest 构造绝对 URL
        # access_url = f"{request._request.build_absolute_uri('/')}downloads/{safe_filename}"

        return sucess_api_response(
            data={
                "physical_path": os.path.abspath(file_path),
                # "access_url": access_url
            },
            status_code=status.HTTP_200_OK
        )

    except Exception as e:
        return fail_api_response(
            error=f"下载失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def deduplicate_documents(document_id):
    """剔除重复的document_id条目

    Args:
        data (dict): 包含文档信息的原始数据，结构示例：
            {
                "code": 0,
                "data": {
                    "answer": "...",
                    "document_id": [
                        {"document_id": "id1", "dataset_id": "set1", ...},
                        {"document_id": "id2", "dataset_id": "set2", ...},
                        ...
                    ]
                }
            }

    Returns:
        dict: 去重后的数据结构，保持原始格式
    """

    # ▼步骤1: 提取文档列表
    # documents = data.get("data", {}).get("document_id", [])

    # ▼步骤2: 初始化去重工具
    seen_ids = set()  # 存储已出现的document_id
    unique_docs = []  # 存储去重后的文档

    # ▼步骤3: 遍历处理每个文档
    for doc in document_id:
        # 关键字段校验（避免KeyError）
        current_id = doc.get("document_id")

        # 情况处理：若document_id缺失则跳过该条目
        if not current_id:
            continue

        # 去重逻辑：仅保留首次出现的ID
        if current_id not in seen_ids:
            seen_ids.add(current_id)
            unique_docs.append(doc)

    # ▼步骤4: 更新数据并返回
    # data["data"]["document_id"] = unique_docs
    return unique_docs


'''
调用命令：
 curl -X POST http://127.0.0.1:8000/api/full-workflow/   -H "Content-Type: application/json"   -d '{
    "question": "我如何使用融合算力平台",
    "database_id": "82dd58ba2dc211f0aa30f68923245abd"
返回结果：
    1.RAG回复。
    2.参考文档ID
'''


@api_view(['POST'])
@permission_classes([CanViewDocument, IsAuthenticated])  # 允许具有 view_document 权限的用户访问
def full_workflow(request):
    database_id = request.data.get("database_id", "82dd58ba2dc211f0aa30f68923245abd")
    chat_id = "b009e858466811f0b5d58e1f51ec9198"
    # Step 1: 创建会话
    session_response = create_session(request, chat_id)
    print(session_response.data)
    if session_response.status_code != 200:
        return fail_api_response(
            error=session_response.data.get("error", "会话创建失败"),
            status_code=session_response.status_code
        )

    # 获取会话ID
    session_id = session_response.data["data"]["session_id"]
    print('成功获取会话ID:', session_id)

    ask_data = {"session_id": session_id, "question": request.data["question"]}
    ask_response = ask_question(  # 使用Django测试客户端模拟请求
        RequestFactory().post('/ask_question/', data=ask_data, format='json'), chat_id
    )
    if ask_response.status_code != 200:
        return fail_api_response(
            error=ask_response.data.get("error", "提问失败"),
            status_code=ask_response.status_code
        )

    # 获取RAG回复的参考文档ID
    document_id = ask_response.data["data"]['reference']
    if not document_id:
        return fail_api_response(
            error="未找到参考文档ID",
            status_code=ask_response.status_code
        )
    # 剔除重复的document_id
    document_id = deduplicate_documents(document_id)
    # 获取RAG回复
    main_answer = ask_response.data["data"]["answer"]

    return sucess_api_response(
        data={
            "answer": main_answer,
            "document_id": document_id
        },
        status_code=status.HTTP_200_OK
    )


# 43f35ddbd1ce4c2091a7fe9f0f0cce91
global_chat_id = "466d5286460e11f0970c5e7fbd49b30f"
temp_base_url = 'http://localhost'


class AskQuestionSerializer(Serializer):
    question = CharField(required=True, max_length=1000)
    session_id = CharField(required=True)


@api_view(['POST'])
def ask_question_stream_request(request):
    serializer = AskQuestionSerializer(data=request.data)
    validate = serializer.is_valid()

    """提问接口"""

    session_id = serializer.validated_data['session_id']
    question = serializer.validated_data['question']

    # 调用RAG服务
    rag_url = f"{temp_base_url}/api/v1/chats/{global_chat_id}/completions"
    # rag_url = f"{BASE_URL}/api/v1/chats/4629079cc6d64a24ad326931320c3012/completions"
    payload = {
        "question": question,
        "stream": True,
        "session_id": '43f35ddbd1ce4c2091a7fe9f0f0cce91'
    }

    # try:
    response = requests.post(rag_url, headers=HEADERS, json=payload, stream=True)
    last_content = ''
    for chunk in response.iter_content(chunk_size=2048):  # 或使用 iter_lines()
        if chunk:
            dict = {}
            try:
                dict = json.loads(chunk[5:])
                # print(dict)
            except Exception as e:
                print(e)

            if dict['data']:
                # 结束
                if isinstance(dict['data'], bool):
                    print('结束')
                    return Response(1)

                content = dict['data']['answer']
                sub_content = content.replace(last_content, '')
                last_content = content

                rect = sub_content

                if reference := dict['data']['reference']:
                    if reference['chunks']:
                        document_ids = ','.join(reference['chunks'])
                        rect = rect + ',references:' + document_ids
                print(rect)

    return Response(1)


@api_view(['POST'])
def ask_question_stream(request):
    # serializer = AskQuestionSerializer(data=request.data)
    # validate = serializer.is_valid()

    """提问接口"""

    # session_id = serializer.validated_data['session_id']
    # question = serializer.validated_data['question']
    question='如何进行城市建设,用中文回答'
    session_id='43f35ddbd1ce4c2091a7fe9f0f0cce91'


    # 调用RAG服务
    rag_url = f"{temp_base_url}/api/v1/chats/{global_chat_id}/completions"
    # rag_url = f"{BASE_URL}/api/v1/chats/4629079cc6d64a24ad326931320c3012/completions"
    payload = {
        "question": question,
        "stream": True,
        "session_id": '43f35ddbd1ce4c2091a7fe9f0f0cce91'
    }

    # try:
    response = requests.post(rag_url, headers=HEADERS, json=payload, stream=True)
    client = SSEClient(response)

    def event_stream():
        full_info=''
        last_think = ''
        last_answer = ''
        for event in client.events():
            if event.data:
                data = json.loads(event.data)
                # print(data)
                code = data['code']
                if code == 102:
                    yield '失败'
                if code == 0:
                    data = data['data']
                    if isinstance(data, bool):
                        print("结束了："+full_info)
                        yield '=========结束=========='
                        # 结束
                        # yield StreamChunk.finish_step('就是结束了', {'promptTokens': 0, 'completionTokens': 0}, False)
                        # yield '结束'
                    else:
                        if answer := data['answer']:
                            # 截取掉上一次返回的内容
                            #sub_answer = answer.replace(last_answer, '')
                            # last_content = answer
                            # 推理部分
                            with_think=answer.startswith('<think>')
                            if with_think:
                                #如果正在推理中
                                if is_thinking := answer.startswith('<think>') and answer.endswith('</think>'):
                                    think_content=answer.replace('<think>', '').replace('</think>', '')
                                    sub_think=think_content.replace(last_think, '')
                                    #记录上一次回答的内容
                                    last_think = think_content
                                    print(f'推理中：{sub_think}')
                                    full_info+=sub_think
                                    # yield sub_think#返回
                                    # yield StreamChunk.reasoning(think_content)#选择脱敏？？？？
                                    yield  StreamChunk.text(sub_think)
                                    continue

                            answer_content=answer.split('</think>')[1].strip()
                            #截取内容
                            sub_answer_content=answer_content.replace(last_answer,'')
                            last_answer=answer_content
                            print(f'正文：{sub_answer_content}')
                            full_info += sub_answer_content
                            yield StreamChunk.text(sub_answer_content)
                            # yield sub_answer_content

    # response = StreamingHttpResponse(
    #     streaming_content=event_stream(),
    #     content_type='text/plain; application/json;charset=utf-8'
    # )

    resp =StreamingHttpResponse(
        streaming_content=(event_stream())
    )
    resp.headers['x-vercel-ai-data-stream'] = 'v1'
    resp.headers['content-type'] = 'application/octet-stream; charset=utf-8'
    return resp

    # response['Transfer-Encoding'] = 'chunked'  # 确保分块传输
    # return response





# session_id='43f35ddbd1ce4c2091a7fe9f0f0cce91'
def chat_stream(request):
    serializer = AskQuestionSerializer(data=request.data)
    validate = serializer.is_valid()
    """提问接口"""
    session_id = serializer.validated_data['session_id']
    question = serializer.validated_data['question']

    # 调用RAG服务
    rag_url = f"{temp_base_url}/api/v1/chats/{global_chat_id}/completions"

    payload = {
        "question": question,
        "stream": True,
        "session_id": session_id
    }
    # try:
    response = requests.post(rag_url, headers=HEADERS, json=payload, stream=True)
    last_content = ''
    for chunk in response.iter_content(chunk_size=2048):  # 或使用 iter_lines()
        if chunk:
            dict = {}
            try:
                dict = json.loads(chunk[5:])
                # print(dict)
            except Exception as e:
                print(e)

            if dict['data']:
                # 结束
                if isinstance(dict['data'], bool):
                    print('结束')
                    return Response(1)

                content = dict['data']['answer']
                sub_content = content.replace(last_content, '')
                last_content = content

                rect = sub_content

                if reference := dict['data']['reference']:
                    if reference['chunks']:
                        document_ids = ','.join(reference['chunks'])
                        rect = rect + ',references:' + document_ids
                print(rect)

    # except Exception as e:
    #     return Response({
    #         "error": f"服务器错误: {str(e)}"
    #     }, status=status.HTTP_500_SERVICE_UNAVAILABLE)

    return Response(1)

#
# @api_view(['POST'])
# def ask_question_stream(request):
#     def event_stream():
#         for i in range(5):
#             # yield  '{{"data":{text}}}\n\n'.format(text=json.dumps(i))
#             yield '0:{text}\n'.format(text=json.dumps(i))
#
#             # sys.stdout.flush()
#             sleep(1)
#
#     # response = StreamingHttpResponse(event_stream(), content_type='text/event-stream; charset=utf-8')
#     response = StreamingHttpResponse(event_stream())
#
#     # response['Connection'] = 'keep-alive'
#     response['X-Accel-Buffering'] = 'no'
#     # response['Content-Type'] = 'text/event-stream; charset=utf-8'
#     response['Content-Type']='application/octet-stream'
#     response['x-vercel-ai-data-stream'] = 'v1'
#
#     # response['Cache-Control'] = 'no-cache, no-transform'
#     # response['Transfer-Encoding'] = 'chunked'
#
#     # response['Content-Type'] = 'text/event-stream'  # 明确指定类型
#     return response

# 我正改raglow的流式数据，目的是改成你说的ai-sdk要求的那种流协议，我自己使用官方提供的前端样例是不是就可以进行测试。