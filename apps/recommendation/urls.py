from django.urls import path

from django.conf import settings
from django.conf.urls.static import static

from .views import create_session, ask_question, download_document, full_workflow
from .views import ask_question_stream

urlpatterns = [
    path('sessions/', create_session, name='create_session'),  # 创建会话（POST）
    path('questions/', ask_question, name='ask_question'),     # 提问（POST）
    path('documents/<str:database_id>/<str:document_id>/',           # 下载文档（GET）
         download_document,
         name='download_document'),
    path('full-workflows/', full_workflow, name='full_workflow'),  # 全流程（POST）
    path('ask_question_stream/',ask_question_stream,name='ask_question_stream'),
    # 流式提问（POST）
    # path('ask/', ask_question_stream, name='ask-stream'),
    # path('test_get/',test_get,name='test_get'),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)


