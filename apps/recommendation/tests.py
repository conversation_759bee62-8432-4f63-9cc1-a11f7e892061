from django.test import TestCase

# Create your tests here.
# filepath: d:\Files\个人文件夹\深度学习\projects\recommendation\app\tests.py
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient

# class APITestCase(TestCase):
#     def setUp(self):
#         self.client = APIClient()
#
#     def test_create_session(self):
#         response = self.client.post(reverse('create_session'))
#         self.assertEqual(response.status_code, 200)
#         self.assertIn('session_id', response.data['data'])
#
#     def test_ask_question(self):
#         # 假设先创建一个会话
#         session_response = self.client.post(reverse('create_session'))
#         session_id = session_response.data['data']['session_id']
#
#         # 测试提问接口
#         response = self.client.post(reverse('ask_question'), {
#             'session_id': session_id,
#             'question': '测试问题'
#         })
#         self.assertEqual(response.status_code, 200)
#         self.assertIn('answer', response.data['data'])
import requests
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from .models import CustomUser, Document
from guardian.shortcuts import assign_perm
BASE_URL = "http://127.0.0.1:8000"
BEARER_TOKEN = "ragflow-I0ZjRlMjhlMzQ1ZTExZjA4NzVkOGUxZj"
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}"
}

class DownloadDocumentTestCase(TestCase):
    def setUp(self):
        # 初始化测试客户端
        self.client = APIClient()

        # 创建用户
        self.teacher = CustomUser.objects.create_user(username='teacher', password='password', role='teacher')
        self.student = CustomUser.objects.create_user(username='student', password='password', role='student')

        # 创建文档
        self.document = Document.objects.create(title='Test Document', content='This is a test document.', owner=self.teacher)

        # 分配权限
        assign_perm('can_download_document', self.teacher, self.document)
        assign_perm('can_download_document', self.student, self.document)
        database_id = "82dd58ba2dc211f0aa30f68923245abd"
        document_id = "3eda69422efc11f0b36ef68923245abd"
        # 定义接口URL
        self.download_document_url = f"{BASE_URL}/api/v1/documents/{database_id}/{document_id}/"

    def test_teacher_can_download_document(self):
        # 老师登录
        self.client.login(username='teacher', password='password')

        # 测试下载文档接口
        response = self.client.get(self.download_document_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIn('physical_path', response.data['data'])


    def test_student_can_download_document(self):
        # 学生登录
        self.client.login(username='student', password='password')

        # 测试下载文档接口
        response = self.client.get(self.download_document_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn('data', response.data)
        self.assertIn('physical_path', response.data['data'])


    def test_unauthenticated_user_cannot_download_document(self):
        # 未登录用户访问
        response = self.client.get(self.download_document_url)
        print('未登录用户访问下载文档接口:', response.status_code)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from .models import CustomUser

from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from .models import CustomUser

class FullWorkflowSingleApiTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.teacher = CustomUser.objects.create_user(username='teacher', password='password', role='teacher')
        self.student = CustomUser.objects.create_user(username='student', password='password', role='student')
        self.url = '/api/v1/full-workflows/'
        self.payload = {
            "question": "我如何使用融合算力平台",
            "stream": False,
            "database_id": "82dd58ba2dc211f0aa30f68923245abd"
        }

    def test_teacher_full_workflow(self):
        self.client.login(username='teacher', password='password')
        response = self.client.post(self.url, self.payload, format='json')
        print("teacher response:", response.status_code, response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('answer', response.data.get('data', {}))
        self.assertIn('document_id', response.data.get('data', {}))

    def test_student_full_workflow(self):
        self.client.login(username='student', password='password')
        response = self.client.post(self.url, self.payload, format='json')
        print("student response:", response.status_code, response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('answer', response.data.get('data', {}))
        self.assertIn('document_id', response.data.get('data', {}))

    def test_unauthenticated_user_cannot_access_full_workflow(self):
        response = self.client.post(self.url, self.payload, format='json')
        print("未登录用户访问全流程接口unauthenticated response:", response.status_code, response.data)
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])