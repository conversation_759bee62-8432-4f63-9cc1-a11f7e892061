from django.db import models

# Create your models here.
from django.contrib.auth.models import AbstractUser
from django.db import models

class CustomUser(AbstractUser):
    ROLE_CHOICES = (
        ('student', 'Student'),
        ('teacher', 'Teacher'),
    )
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='student')

from django.db import models
from django.conf import settings

class Document(models.Model):
    title = models.CharField(max_length=255, verbose_name="文档标题")
    content = models.TextField(verbose_name="文档内容")
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="documents",
        verbose_name="所有者"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        permissions = [
            ("can_view_document", "Can view document"),
            ("can_download_document", "Can download document"),
        ]
        verbose_name = "文档"
        verbose_name_plural = "文档"

    def __str__(self):
        return self.title