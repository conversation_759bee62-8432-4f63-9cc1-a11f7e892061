from django.contrib.auth.models import AbstractUser
from django.db import models

from apps.recommendation.models import CustomUser




class Conversation(models.Model):
    id=models.CharField(max_length=100,primary_key=True)
    title=models.CharField(max_length=1000)
    user=models.ForeignKey(CustomUser,on_delete=models.CASCADE)
    create_at=models.DateTimeField(auto_now_add=True)
    update_at=models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversation'
        verbose_name='会话'
        ordering=('create_at',)


class ChatMessage(models.Model):
    content=models.TextField()
    conversation=models.ForeignKey(Conversation,on_delete=models.CASCADE)
    ROLE_TYPE_CHOICES = (
        ("SYSTEM", "system"),
        ("HUMAN", "user"),
        ('TOOL','tool'),
        ('Assistant','assistant')
    )
    role=models.CharField(max_length=20,choices=ROLE_TYPE_CHOICES,default='HUMAN')
    create_at = models.DateTime<PERSON>ield(auto_now_add=True)
    update_at = models.DateTimeField(auto_now=True)
    class Meta:
        db_table = 'chat_message'
        verbose_name='聊天内容'
        ordering=('pk',)