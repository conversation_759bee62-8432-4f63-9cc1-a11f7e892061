
from langgraph.checkpoint.redis import RedisSaver
from langgraph.graph.state import CompiledStateGraph
from matplotlib import pyplot as plt, image as mpimg
from langchain_openai import ChatOpenAI, OpenAI
from ragflow.settings import REDIS_URL, MODEL, API_KEY, BASE_URL, COMMON_CHAT_MAX_TOKEN
import json
from typing import TypedDict, Annotated, Sequence, Iterator, Any, Optional

from langchain.chat_models import init_chat_model
from langchain_community.chat_models import ChatTongyi
from langgraph.checkpoint.base import CheckpointTuple
from langgraph.checkpoint.redis import RedisSaver

from django.http import StreamingHttpResponse
from langchain_core.messages import SystemMessage, HumanMessage, trim_messages, BaseMessage
from langchain_core.prompts import PromptTemplate, ChatPromptTemplate, MessagesPlaceholder
from langchain_openai import ChatOpenAI, OpenAI

from langgraph.checkpoint.memory import MemorySaver, InMemorySaver
from langgraph.graph import START, MessagesState, StateGraph, add_messages
from langgraph.graph.state import CompiledStateGraph
from matplotlib import pyplot as plt, image as mpimg


def show(graph:  CompiledStateGraph):
    try:
        # 使用 Mermaid 生成图表并保存为文件
        mermaid_code = graph.get_graph().draw_mermaid_png()
        with open("graph.jpg", "wb") as f:
            f.write(mermaid_code)

        # 使用 matplotlib 显示图像
        img = mpimg.imread("graph.jpg")
        plt.imshow(img)
        plt.axis('off')  # 关闭坐标轴
        plt.show()
    except Exception as e:
        print(f"An error occurred: {e}")




