import json
from typing import TypedDict, Annotated, Sequence, Iterator, Any, Optional

from django.db import transaction
from django.http import StreamingHttpResponse

from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet, ModelViewSet

from apps.chat.agents.common_chat_agant import checkpointer, common_chat_graph, common_stream_response
from apps.chat.models import Conversation, ChatMessage
from apps.chat.serializers import ChatMessageSerializer

from apps.recommendation.streams import StreamChunk
from utils.viewset import CustomModelViewSet


# config = {"configurable": {"thread_id": "usual:user01:conversation01"}}


@api_view(['POST'])
def chat(request):
    streamProtocol = request.data["streamProtocol"]
    message = request.data['messages']
    checkpoint_tuple = checkpointer.get_tuple(config)
    if checkpoint_tuple is None:
        print('需要从pg中拿历史记录，追加到信息的最前面，去掉systemmessage')
        pass
    save_messages = ''
    print('保存用户问题')

    def stream_response():
        is_data = streamProtocol == 'data'
        save_messages_list = []
        for chunk, meta_data in common_chat_graph.stream({"messages": message}, config=config, stream_mode='messages'):

            save_messages_list.append(chunk.content)

            if 'finish_reason' in chunk.response_metadata and chunk.response_metadata['finish_reason'] == 'stop':
                print('结束')
                save_messages = ''.join(save_messages_list)
                print('保存')
                yield StreamChunk.finish_message('stop', {'input_tokens': 0, 'output_tokens': 0}) if is_data else ''
            else:
                yield StreamChunk.text(chunk.content) if is_data else chunk.content

    resp = StreamingHttpResponse(streaming_content=stream_response())
    resp.headers['content-type'] = 'application/octet-stream; charset=utf-8'
    resp.headers['x-vercel-ai-data-stream'] = 'v1'
    return resp


class RedisStoreMsg(TypedDict):
    content: str
    type: str


@api_view(['GET'])
def test_get_saverinfo(request):
    checkpoint_tuple = checkpointer.get_tuple(config)

    if checkpoint_tuple is None:
        # Redis 中没有检查点，去 PostgreSQL 加载历史会话
        print("Redis 中未找到检查点，加载历史会话...")
        # 在此处实现从 PostgreSQL 加载历史会话的逻辑
    else:
        print(f'有检查点信息：{checkpoint_tuple}')
    return Response({'messages': 1})


class ConversitionViewSet(CustomModelViewSet):
    pass


class ChatViewSet(CustomModelViewSet):
    serializer_class = ChatMessageSerializer
    queryset = ChatMessage.objects.all()

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        conversation_id = request.data["id"]
        content = request.data["messages"][-1]["content"]
        title = request.data["messages"][0]["content"]
        streamProtocol = request.data["streamProtocol"]
        # 创建会话
        conversation = Conversation.objects.filter(pk=conversation_id).first()
        if created:=conversation is None:
            conversation = Conversation.objects.create(pk=conversation_id, title=title, user=request.user, )
        # 创建用户消息
        ChatMessage(conversation=conversation, content=content, role='HUMAN', ).save()
        config = {"configurable": {"thread_id": f'common_chat:{request.user.id}:{conversation.id}'}}

        if not created and (checkpoint_tuple := checkpointer.get_tuple(config)) is None:
            print('需要从pg中拿历史记录，追加到信息的最前面，去掉systemmessage')

        def create_ai_msg(ai_messages_chunk):
            ai_message = ''.join(ai_messages_chunk)
            ai_msg = {
                'conversation_id': conversation_id,
                'content': ai_message,
                'role': 'Assistant',
            }
            ChatMessage.objects.create(**ai_msg)  # 保存助手回复

        resp = StreamingHttpResponse(
            streaming_content=common_stream_response(streamProtocol, conversation.id, content, config, create_ai_msg))
        resp.headers['content-type'] = 'application/octet-stream; charset=utf-8'
        resp.headers['x-vercel-ai-data-stream'] = 'v1'
        return resp
    # def list(self, request, *args, **kwargs):

