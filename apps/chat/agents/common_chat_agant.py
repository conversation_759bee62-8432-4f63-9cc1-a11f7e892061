from typing import TypedDict, Annotated, Sequence
from django.http import StreamingHttpResponse
from langchain_core.messages import BaseMessage, trim_messages
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.redis import RedisSaver
from langgraph.constants import START
from langgraph.graph import add_messages, StateGraph
from apps.chat.prompts import common_chat_template
from apps.recommendation.streams import StreamChunk
from ragflow.settings import MODEL, API_KEY, BASE_URL, REDIS_URL

COMMON_CHAT_MAX_TOKEN=10000
class State(TypedDict):
    messages: Annotated[Sequence[BaseMessage], add_messages]


workflow = StateGraph(state_schema=State)

common_chat_llm = ChatOpenAI(
    # model="QwQ-think",
    model=MODEL,
    openai_api_key=API_KEY,
    # openai_api_base="http://localhost:8080/v1",
    base_url=BASE_URL,
    max_tokens=COMMON_CHAT_MAX_TOKEN,
    # temperature=1,
    # extra_body={"enable_thinking": True}
    # extra_body={
    #     "enable_thinking":True
    # }
    # model_kwargs = {
    #     "thinking": True,  # 假设这是模型的深度思考参数
    #     "reasoning_steps": 3  # 其他可能的参数
    # }
)
def custom_token_counter(messages):
    return sum(len(message.content) for message in messages)

trimmer = trim_messages(
        max_tokens=COMMON_CHAT_MAX_TOKEN,
        strategy="last",
        token_counter=custom_token_counter,
        include_system=True,
        allow_partial=False,
        start_on="human",
    )

with RedisSaver.from_conn_string(REDIS_URL) as checkpointer:
    checkpointer.setup()
    # Call setup to initialize indices
    # checkpointer.setup()
    # checkpoint = {
    #     "v": 1,
    #     "ts": "2024-07-31T20:14:19.804150+00:00",
    #     "id": "1ef4f797-8335-6428-8001-8a1503f9b875",
    #     "channel_values": {
    #         "my_key": "meow",
    #         "node": "node"
    #     },
    #     "channel_versions": {
    #         "__start__": 2,
    #         "my_key": 3,
    #         "start:node": 3,
    #         "node": 3
    #     },
    #     "versions_seen": {
    #         "__input__": {},
    #         "__start__": {
    #             "__start__": 1
    #         },
    #         "node": {
    #             "start:node": 2
    #         }
    #     },
    #     "pending_sends": [],
    # }
    #
    # # Store checkpoint
    # checkpointer.put(write_config, checkpoint, {}, {})
    #
    # # Retrieve checkpoint
    # loaded_checkpoint = checkpointer.get(read_config)
    #
    # # List all checkpoints
    # checkpoints = list(checkpointer.list(read_config))
def call_model(state: State):
    messages = trimmer.invoke(state['messages'])
    state['messages'] = messages
    # s=''
    # for message in messages:
    #     s+= f"{message.content}\n"
    # # print(f'本地提问：{s}')
    prompt = common_chat_template.invoke(state)
    response = common_chat_llm.invoke(prompt)
    return {'messages': response}

workflow.add_edge(START, 'model')
workflow.add_node('model', call_model)

common_chat_graph = workflow.compile(checkpointer=checkpointer)


def common_stream_response(streamProtocol,conversation_id,message,config,finish_callback):
    is_data_type = streamProtocol == 'data'
    ai_messages_chunk = []
    i:int=0
    for chunk, meta_data in common_chat_graph.stream({"messages": message}, config=config, stream_mode='messages'):
        ai_messages_chunk.append(chunk.content)
        i+=1
        if 'finish_reason' in chunk.response_metadata and chunk.response_metadata['finish_reason'] == 'stop':
            print('结束')
            finish_callback(ai_messages_chunk)
            yield StreamChunk.finish_message('stop', {'input_tokens': 0, 'output_tokens': 0}) if is_data_type else ''
        else:
            # yield StreamChunk.text(chunk.content) if is_data_type else chunk.content #正常返回 ✅
            # yield StreamChunk.reasoning('我想想')if i<10 else StreamChunk.text(chunk.content) #模拟思考 ✅
            yield StreamChunk.sourse('文章。。。') if i==5 else StreamChunk.text(chunk.content) #模拟sourse ❎


