from langchain_openai import ChatOpenAI, OpenAI

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph

model = 'qwen2.5-7B'
# base_url='192.168.5.249:31196/v1/chat/completions
base_url='192.168.5.249:31196/v1'
api_key= 'tianhe-linyi'

llm=ChatOpenAI(
    model=model,
    openai_api_base=base_url,
    openai_api_key=api_key,
    max_tokens=40000,
    temperature=1,
    streaming=True)

resp = llm.invoke("介绍一下自己吧")
print(resp)
client = OpenAI(
        # 若没有配置环境变量，请用阿里云百炼API Key将下行替换为：api_key="sk-xxx",
        api_key=api_key,
        base_url=base_url,
    )


if __name__== "__main__":
    completion = client.chat.completions.create(model="qwen-plus", messages="介绍一下自己吧")
    # resp = llm.invoke("介绍一下自己吧")
    # print(resp)
# Define a new graph
# workflow = StateGraph(state_schema=MessagesState)
#
#
# # Define the function that calls the model
# def call_model(state: MessagesState):
#     response = model.invoke(state["messages"])
#     return {"messages": response}
#
#
# # Define the (single) node in the graph
# workflow.add_edge(START, "model")
# workflow.add_node("model", call_model)
#
# # Add memory
# memory = MemorySaver()
# app = workflow.compile(checkpointer=memory)


