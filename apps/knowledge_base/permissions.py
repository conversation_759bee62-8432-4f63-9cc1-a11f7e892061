from rest_framework import authentication
from rest_framework.permissions import BasePermission
from guardian.shortcuts import get_perms




class CanManageDataset(BasePermission):
    """
    仅允许老师（有 manage_dataset 权限）增删改知识库
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role == 'teacher'

class CanViewDataset(BasePermission):
    """
    允许有 view_dataset 权限的用户（老师和学生）查看知识库
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated

class DevUserAuthentication(authentication.BaseAuthentication):

    def authenticate(self, request):
        from apps.recommendation.models import CustomUser
        user = CustomUser.objects.get(id=2)
        return (user, None)