from django.urls import path
from . import views
from .views import DocumentListView, DocumentDetailView, parse_documents, stop_parse_documents
from .views import ChunkListView, ChunkDetailView, retrieval_chunks

urlpatterns = [
    path('datasets/', views.DatasetListCreateView.as_view()),            # GET/POST
    path('datasets/<str:dataset_id>/', views.DatasetDetailView.as_view()), # GET/PUT/DELETE
    path('datasets/<str:dataset_id>/documents', DocumentListView.as_view()),  # GET/POST/DELETE
    path('datasets/<str:dataset_id>/documents/<str:document_id>', DocumentDetailView.as_view()),  # GET/PUT
    path('datasets/<str:dataset_id>/parse-tasks', parse_documents, name='parse_documents'),  # POST
    path('datasets/<str:dataset_id>/parse-tasks', stop_parse_documents, name='stop_parse_documents'),  # DELETE
    # Chunk管理（增、查、删）
    path(
        'datasets/<str:dataset_id>/documents/<str:document_id>/chunks/',
        ChunkListView.as_view(),
        name='chunk-list'
    ),
    # Chunk更新
    path(
        'datasets/<str:dataset_id>/documents/<str:document_id>/chunks/<str:chunk_id>/',
        ChunkDetailView.as_view(),
        name='chunk-detail'
    ),
    # Chunk检索
    path(
        'chunk-retrievals/',
        retrieval_chunks,
        name='retrieval-chunks'
    ),
]