# from rest_framework.test import APITestCase, APIClient
# from django.urls import reverse
#
# class DatasetAPITestCase(APITestCase):
#     def setUp(self):
#         self.client = APIClient()
#         # 如果需要认证，可以在这里设置token
#         self.client.credentials(HTTP_AUTHORIZATION='Bearer ragflow-I0ZjRlMjhlMzQ1ZTExZjA4NzVkOGUxZj')
#         self.base_url = 'http://127.0.0.1:8000/api/v1/datasets/'
#
#     # def test_create_dataset(self):
#     #     print('创建知识库')
#     #     data = {
#     #         "name": "test_1",
#     #         "description": "测试知识库"
#     #     }
#     #     response = self.client.post(self.base_url, data, format='json')
#     #     self.assertEqual(response.status_code, 200)
#     #     self.assertIn('data', response.data)
#     #
#     # def test_list_datasets(self):
#     #     print('列出知识库')
#     #     response = self.client.get(self.base_url)
#     #     print(response.data)
#     #     self.assertEqual(response.status_code, 200)
#     #     self.assertIn('data', response.data)
#
#     # def test_update_dataset(self):
#     #     print('更新知识库')
#     #     dataset_id = "0ab44de6412011f0b7608e1f51ec9198"  # 替换为实际的ID
#     #     data = {
#     #         "description": "更新描述"
#     #     }
#     #     url = f"{self.base_url}{dataset_id}/"
#     #     response = self.client.put(url, data, format='json')
#     #     # 这里假设更新成功返回200
#     #     self.assertEqual(response.status_code, 200)
#     #     self.assertIn('data', response.data)
#
#     def test_delete_dataset(self):
#         print('删除知识库')
#         dataset_id = "1fd8f4bc411e11f0829b8e1f51ec9198"
#         url = f"{self.base_url}/{dataset_id}/"
#         response = self.client.delete(url)
#         print(response.data)
#         self.assertEqual(response.status_code, 200)

from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from io import BytesIO

class DocumentApiTest(APITestCase):
    def setUp(self):
        self.token = "Bearer ragflow-I0ZjRlMjhlMzQ1ZTExZjA4NzVkOGUxZj"
        self.dataset_id = "0ab44de6412011f0b7608e1f51ec9198"
        self.headers = {"HTTP_AUTHORIZATION": self.token}
        self.test_file_content = b"test content"
        self.test_file_name = "ragflow.txt"

    def test_upload_document(self):
        # 上传文档
        url = f"/api/v1/datasets/{self.dataset_id}/documents"
        file_obj = BytesIO(self.test_file_content)
        file_obj.name = self.test_file_name
        response = self.client.post(url, {'file': file_obj}, format='multipart', **self.headers)
        self.assertEqual(response.status_code, 200)
        doc_id = response.data['data'].get('id') or response.data['data'].get('document_id')
        self.assertIsNotNone(doc_id)

    def test_list_document(self):
        # 列出文档
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)

    def test_download_document(self):
        # 下载文档
        download_url = f"/api/v1/datasets/{self.dataset_id}/documents/{doc_id}"
        response = self.client.get(download_url, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content, self.test_file_content)

    def test_update_document(self):
        # 更新文档配置
        update_url = f"/api/v1/datasets/{self.dataset_id}/documents/{doc_id}"
        update_data = {
            "name": "updated.txt",
            "chunk_method": "manual",
            "parser_config": {"chunk_token_count": 128}
        }
        response = self.client.put(update_url, update_data, format='json', **self.headers)
        self.assertEqual(response.status_code, 200)

    def test_parse_document(self):
        # 解析文档
        parse_url = f"/api/v1/datasets/{self.dataset_id}/chunks"
        response = self.client.post(parse_url, {"document_ids": [doc_id]}, format='json', **self.headers)
        self.assertEqual(response.status_code, 200)

    def test_stop_parse_document(self):
        # 停止解析
        response = self.client.delete(parse_url, {"document_ids": [doc_id]}, format='json', **self.headers)
        self.assertEqual(response.status_code, 200)

    def test_delete_document(self):
        # 批量删除文档
        response = self.client.delete(url, {"ids": [doc_id]}, format='json', **self.headers)
        self.assertEqual(response.status_code, 200)