from django.db import transaction
from guardian.shortcuts import assign_perm
from rest_framework.views import APIView
from rest_framework.response import Response

import requests
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser,JSONParser
from rest_framework.decorators import api_view, parser_classes

from apps.knowledge_base.models import Knowledge

BASE_URL = "http://192.168.5.250:20000"
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer ragflow-I0ZjRlMjhlMzQ1ZTExZjA4NzVkOGUxZj"
}

# 返回格式规范
def sucess_api_response(data=None,status=200):
    return Response({"data": data,},status=status)
def fail_api_response( error=None,status=500):
    '''
    {} 里的内容是返回给前端的实际数据。
    status= 是告诉浏览器/客户端本次请求的 HTTP 状态码。
    '''
    return Response({"error": error,},status=status)

'''------------知识库管理接口---------'''
'''
    RESTful 设计中：
    
    DatasetListCreateView 适合处理“集合资源”的操作，比如：
    
    GET /datasets/：获取所有知识库（列表）
    POST /datasets/：新建知识库
    DELETE /datasets/：批量删除（如果有此需求）
    DatasetDetailView 适合处理“单个资源”的操作，比如：
    
    GET /datasets/<dataset_id>/：获取单个知识库详情
    PUT /datasets/<dataset_id>/：更新单个知识库
    DELETE /datasets/<dataset_id>/：删除单个知识库
'''
class DatasetListCreateView(APIView):
    def get(self, request):
        # 列出知识库
        resp = requests.get(f"{BASE_URL}/api/v1/datasets", headers=HEADERS, params=request.query_params)
        return sucess_api_response(resp.json(), status=resp.status_code)

    @transaction.atomic
    def post(self, request):
        # 创建知识库
        resp = requests.post(f"{BASE_URL}/api/v1/datasets", headers=HEADERS, json=request.data)
        #创建知识库数据
        k=  {
            'name':request.data['name'],
             'description':request.data['description'],
             'author':request.data['author']}
        instance=Knowledge.objects.create(**k)
        #授权给当前用户
        for codename in Knowledge._meta.permissions:
            assign_perm(codename, request.user, instance)
        return sucess_api_response(resp.json(), status=resp.status_code)

class DatasetDetailView(APIView):
    def get(self, request, dataset_id):
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}"
        resp = requests.get(url, headers=HEADERS)
        return Response(resp.json(), status=resp.status_code)

    def put(self, request, dataset_id):
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}"
        resp = requests.put(url, headers=HEADERS, json=request.data)
        return Response(resp.json(), status=resp.status_code)

    def delete(self, request, dataset_id):
        resp = requests.delete(f"{BASE_URL}/api/v1/datasets/{dataset_id}", headers=HEADERS)
        return sucess_api_response(resp.json(), status=resp.status_code)
'''----------知识库管理接口结束--------'''

'''------------知识库构建接口---------'''
class DocumentListView(APIView):
    parser_classes = [MultiPartParser, FormParser, JSONParser]

    def get(self, request, dataset_id):
        """列出某个知识库里的文档"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        resp = requests.get(url, headers=HEADERS, params=request.query_params)
        return sucess_api_response(resp.json(), resp.status_code)

    def post(self, request, dataset_id):
        """上传文档，支持多文件"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        file_list = request.FILES.getlist('file')
        if not file_list:
            return fail_api_response({"code": 101, "message": "No file part!"}, status=400)
        files = [('file', (f.name, f, f.content_type)) for f in file_list]
        # 只传递 Authorization 头，requests 会自动处理 multipart
        resp = requests.post(url, headers={"Authorization": HEADERS["Authorization"]}, files=files)
        return sucess_api_response(resp.json(), resp.status_code)

    def delete(self, request, dataset_id):
        """批量删除文档"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        resp = requests.delete(url, headers={**HEADERS, "Content-Type": "application/json"}, json=request.data)
        return sucess_api_response(resp.json(), resp.status_code)


class DocumentDetailView(APIView):
    def get(self, request, dataset_id, document_id):
        """下载文档"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        resp = requests.get(url, headers=HEADERS, stream=True)
        response = Response(resp.raw, content_type=resp.headers.get('Content-Type', 'application/octet-stream'))
        response['Content-Disposition'] = resp.headers.get('Content-Disposition', f'attachment; filename="{document_id}.bin"')
        return response

    def put(self, request, dataset_id, document_id):
        """更新文档配置"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        resp = requests.put(url, headers={**HEADERS, "Content-Type": "application/json"}, json=request.data)
        return sucess_api_response(resp.json(), status=resp.status_code)

# 解析/停止解析接口
@api_view(['POST'])
def parse_documents(request, dataset_id):
    url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/chunks"
    resp = requests.post(url, headers={**HEADERS, "Content-Type": "application/json"}, json=request.data)
    return sucess_api_response(resp.json(), resp.status_code)

@api_view(['DELETE'])
def stop_parse_documents(request, dataset_id):
    url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/chunks"
    resp = requests.delete(url, headers={**HEADERS, "Content-Type": "application/json"}, json=request.data)
    return sucess_api_response(resp.json(), resp.status_code)
'''----------知识库构建接口结束--------'''

'''----------chunk管理接口---------'''
class ChunkListView(APIView):
    parser_classes = [JSONParser]

    def post(self, request,dataset_id, document_id):
        """添加chunk"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        resp = requests.post(url, headers=HEADERS, json=request.data)
        return Response(resp.json(), status=resp.status_code)

    def get(self, request, dataset_id, document_id):
        """列出chunk"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        resp = requests.get(url, headers={"Authorization": HEADERS["Authorization"]}, params=request.query_params)
        return Response(resp.json(), status=resp.status_code)

    def delete(self, request, dataset_id, document_id):
        """删除chunk"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        resp = requests.delete(url, headers=HEADERS, json=request.data)
        return Response(resp.json(), status=resp.status_code)

class ChunkDetailView(APIView):
    parser_classes = [JSONParser]

    def put(self, request, chunk_id, dataset_id, document_id):
        """更新chunk"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}"
        resp = requests.put(url, headers=HEADERS, json=request.data)
        return Response(resp.json(), status=resp.status_code)

@api_view(['POST'])
def retrieval_chunks(request):
    """检索chunk"""
    url = f"{BASE_URL}/api/v1/retrieval"
    resp = requests.post(url, headers=HEADERS, json=request.data)
    return Response(resp.json(), status=resp.status_code)
'''----------chunk管理接口结束---------'''

