from django.db import models

# Create your models here.

class Knowledge(models.Model):
    id = models.AutoField(verbose_name="ID", primary_key=True)
    title = models.CharField(verbose_name="标题", max_length=200)
    description=models.TextField(verbose_name="描述", blank=True, null=True)
    author = models.CharField(verbose_name="作者", max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    class Meta:
        db_table = "knowledge"
        verbose_name = "知识库"
        verbose_name_plural = verbose_name
        default_permissions=()
        permissions = [
            ("view_knowledge", "查看知识库"),
            ("add_knowledge", "添加知识库"),
            ("change_knowledge", "修改知识库"),
            ("delete_knowledge", "删除知识库"),
        ]
    def __str__(self):
        return self.title
    pass