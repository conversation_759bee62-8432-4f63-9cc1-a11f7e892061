# Generated by Django 4.2.19 on 2025-06-18 02:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Knowledge',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('author', models.CharField(blank=True, max_length=100, null=True, verbose_name='作者')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '知识库',
                'verbose_name_plural': '知识库',
                'db_table': 'knowledge',
                'permissions': [('view_knowledge', '查看知识库'), ('add_knowledge', '添加知识库'), ('change_knowledge', '修改知识库'), ('delete_knowledge', '删除知识库')],
                'default_permissions': (),
            },
        ),
    ]
