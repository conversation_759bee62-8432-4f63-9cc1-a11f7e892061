[uwsgi]
# 绑定到所有网络接口（允许外部访问）
http = 0.0.0.0:8001
# 启用主进程（更稳定）
master = true
# 开发模式建议单进程
processes = 1
# 虚拟环境路径（确认是否正确）
virtualenv = /opt/homebrew/Caskroom/miniconda/base/envs/lykz
# 项目目录和 WSGI 模块
chdir = /Users/<USER>/Documents/code/tianhe/lykz/ragflow_wangju
module = ragflow.wsgi:application
# 开发时启用自动重载
py-autoreload = 1

# 调整请求头大小限制（单位：字节）
buffer-size = 65535
# 如果需要处理超大请求体（如文件上传）
max-request-body-size = 1073741824  # 1GB
# 禁用请求头严格检查（针对某些代理服务器）
disable-http-keepalive = true